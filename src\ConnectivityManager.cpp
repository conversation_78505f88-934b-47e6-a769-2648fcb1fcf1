#include "ConnectivityManager.h"
#include "PowerManager.h"
#include "Config.h"
#include "esp_wifi.h"  // 包含 esp_wifi_erase_ap_info() 的定义
#include "nvs.h"       // 包含 NVS 操作（如 nvs_open()）的定义
#include "nvs_flash.h" // 若用到 nvs_flash_erase()，也需包含
#include <Arduino.h>
#include <esp_log.h> // 添加头文件

static const char *TAG = "ConnectivityManager";
// Static member definitions
const char *ConnectivityManager::BLUFI_POP = "abcd1234";
const char *ConnectivityManager::BLUFI_SERVICE_NAME = "PROV_FishTank_SENSOR";
const char *ConnectivityManager::PREF_NAMESPACE = "connectivity";
const char *ConnectivityManager::PREF_WIFI_FAILURE_COUNT = "wifi_failures";
const char *ConnectivityManager::NTP_SERVER1 = "ntp.aliyun.com";
const char *ConnectivityManager::NTP_SERVER2 = "cn.pool.ntp.org";
const char *ConnectivityManager::NTP_SERVER3 = "pool.ntp.org";
ConnectivityManager *ConnectivityManager::instance = nullptr;

ConnectivityManager::ConnectivityManager(const char *mqttServer, uint16_t mqttPort) : mqttServer(mqttServer),
                                                                                      mqttPort(mqttPort),
                                                                                      mqttClient(wifiClient),
                                                                                      initialized(false),
                                                                                      wifiConnected(false),
                                                                                      mqttConnected(false),
                                                                                      provisioningActive(false),
                                                                                      wifiFailureCount(0),
                                                                                      powerManager(nullptr)
{
    instance = this;
}

ConnectivityManager::~ConnectivityManager()
{
    disconnect();
    preferences.end();
}

bool ConnectivityManager::begin()
{
    DEBUG_PRINTLN("ConnectivityManager::begin");
    // Initialize preferences
    preferences.begin(PREF_NAMESPACE, false);//指定命名空间，可读写
    loadWiFiFailureCount();//加载 WiFi 失败次数

    generateDeviceId(); // 生成设备ID
    setupTopics();      // MQTT主题
    
    this->deviceId = deviceId; // 设备ID
    this->mqttUser = deviceId; // MQTT用户名
    this->mqttPassword = "123"; // MQTT密码

    this->jetlinksProductId = "1960163782781816832"; // JetLinks 产品 ID
    this->jetlinksDeviceId = "1960614044855218176"; // JetLinks 设备 ID
    this->jetlinksDeviceKey = "123"; // JetLinks 设备密钥
   
    // Setup MQTT client with TLS if port 8883
    if (mqttPort == 8883)
    {
        // Configure secure client for TLS with certificate
        const char *rootCA = R"MQTTCA(
-----BEGIN CERTIFICATE-----
MIIFjTCCA3WgAwIBAgIUG245sAGD8x+xQmoMJjexTzg4PDEwDQYJKoZIhvcNAQEL
BQAwVjELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUd1YW5nZG9uZzERMA8GA1UEBwwI
RG9uZ2d1YW4xCzAJBgNVBAoMAkhRMRMwEQYDVQQDDApIUSBSb290IENBMB4XDTI1
MDgyNzAzNDgwN1oXDTQ1MDgyMjAzNDgwN1owVjELMAkGA1UEBhMCQ04xEjAQBgNV
BAgMCUd1YW5nZG9uZzERMA8GA1UEBwwIRG9uZ2d1YW4xCzAJBgNVBAoMAkhRMRMw
EQYDVQQDDApIUSBSb290IENBMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKC
AgEAlPk6cmSfWwozS3ee8KYQv8X6n4GvTHdO7dDAZD/cWRCNnMXkNUrzMEsqa3qA
Y+XHMk2Qr6q2Jm2fX9dLbQyrUlVUAU6SzVrpCgsEYxppMcV9lELqLMoVcps4fL+L
OemOeaOojNz+Zj8x0fla/6mx5nttFOaGjUiXEEDDLieRdyu+XBMBKAj5hLEErzaj
pKcFpQcnOO+UCPX8yGIMGnpu0ReWJ3YYVKRzIhCsSrWrsPwjy32KEeZR1W4Ti+c1
CJ9bznnha0Q782ujs53Zaw2X7u6YWqdT87iMwPLccnhKZWv2C6ljwvYhWEB/4mPB
uyOJL8cdcfesKJhfjNP8k58tDhJfpK8Oc4XCzXGnj3NfQ8UVoU+apdNnUWY5+ken
60lx9/KVAu/h5BgQkYPK+ojauQSqrTG2xwW9lRotZ0QayuBx/RCmKLrr2ksoYlh8
vlTepMChrHt1AwQxFB6/KJohjNErnfsGUSer/NtZR6I0E42c9NUELs5+3tT3FpOe
02Cr1gdaabDPmN7q+oZib9N4LCE90RA5D6bMAa05/ho3WruPHSVBgSZN70Jd9Qlv
4ADbaimBlFdpJdOSSacJ8ZG0lFeBPABqrmRj/LIWHTglmIEOD2MkI4k33wZ868ld
GmFA7lTVTx0TISpxGU0Tfcx8pkFS12JmlPOfTo3mBUfKh+cCAwEAAaNTMFEwHQYD
VR0OBBYEFMo53TtBMMTc7bLXMOiuixFcZIb/MB8GA1UdIwQYMBaAFMo53TtBMMTc
7bLXMOiuixFcZIb/MA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggIB
AHzWGEZ89/5XLVoMfhfHbZydHwcNV/tGci9FmqFMM2G6bhX7MgyBOu6CfJk5DsEA
4OON75Xb8rBlQttLKDyZif6h/kF9v6sOqHZTj/QV3NNBO0nSx1ul1ldEh+YXzvGr
rFhWNhHouyuUU/XqfptN3JFYXE4QHDPju5NpgGqep7C4eK29yQnumoaEesgT+Zkc
tN5UedsdB/dT4MYci0MKl64Sr/0ykCW0oeVGI5qkV5TUOVUDD/zyuz6befUGtbmN
2p2CvG+GV+omN+rQSnNCR4PqjKBTup5qePBo/SOA2Z4iV3Z+CMuDrFu0YNikZHjL
YurACoDt+xdnt2/mdJpTaIfrb0xT3IkKkNpfyq4PTPE3gfXh7pILf9HzjFD2Vrpy
xC1JVCC+PyMXVjqsLRUMZZ1sg6y7e3LOZ5CNagR0b7Bkt5LkitpOgbay43Hulyvb
pQKzAg2NaR/RpgSNCw0xhslc5nRTIgCMmII8AYN25lC4vfClImVXiakRcGJtrybE
pyKC6W3K6QQLiNY/kklcNlVl95iZje3pYVqORHHdth4oIjwnKj30eL9kNroOR25G
E+b//VAkCi14oVo89fnETaq4vigZSXNlx+cKG26RydkP2OFTr6oc6s9KLDhF4vXL
fGrZUHGTHcjKQ2of0mpFdSOlBZg9CBz3MjoNqSuyT4It
-----END CERTIFICATE-----
)MQTTCA";

        wifiClientSecure.setCACert(rootCA);  // 设置 CA 证书

        // 设置客户端证书和私钥
        const char *clientCert = R"CERT(
-----BEGIN CERTIFICATE-----
MIIFNDCCAxwCFDvDYa8pgxOMRai3xb+9oeFh1FjzMA0GCSqGSIb3DQEBCwUAMFYx
CzAJBgNVBAYTAkNOMRIwEAYDVQQIDAlHdWFuZ2RvbmcxETAPBgNVBAcMCERvbmdn
dWFuMQswCQYDVQQKDAJIUTETMBEGA1UEAwwKSFEgUm9vdCBDQTAeFw0yNTA4Mjkw
MTMxMjdaFw00NTA4MjQwMTMxMjdaMFcxCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAlH
dWFuZ2RvbmcxETAPBgNVBAcMCERvbmdndWFuMQswCQYDVQQKDAJIUTEUMBIGA1UE
AwwLdGVzdC5jbGllbnQwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDH
mfT7pVWkX58kZgWzqXMnnKoO3Wqer92TU+i4K+ED57x2yZtzAF1NzA6HEcklUjxT
ig21NDpvzy1CnmYrkF5NP/E4LIrTNtQ64zLogKVxJWdFqzL2hPlSZVsrQ0kDq7fa
I+9FE+yxLzt4fYVMZ0+hTyr00dvEEuNWE1ffnt/bcjh/YGu06xBdnrITOVI5BtZX
2CGCpCFGnImyYcDl17ELfGEY98jHH8HauOatdkMhVpwGfeWYJlGe5ApvX0lT+uaW
ShCfdxPs8QLLIZiB036eIz80a0X1QTdZuW63bFtkoHdHhx3LNuGCGDLclllE97qx
3UkcDL9u8BzxVeegKyiHn460FaT9q2szA6Zn8/5+PmyWKp9CvPvOHobqwCNnRB5z
M/TWBDVEYf06OH6oLi/yCf8QIJBVUcz96lAujILrcUDjpgdCcCDaXvd/kj/yGnlK
VY08N9CxcdYC23qpBHkIiZSrARMclkVaueqL1KRL5J9mbUmzeK3U1oG9C27Ae9+O
RVUNUldqjvaUyN9La2w/5gXLUcOpeOUzgF5VSA5kD4B6YFcpoMD9QW64b1vHMmKP
iw4Ax5IsG4LLvUFs1LFeFbt8uTI7BdzudnwILtg64fgZjc4wqi+VoTNzT/UX1vH+
9xPXAd0dgH6cjqyDXSldELrdFgk9z9ECFSDYKzrqywIDAQABMA0GCSqGSIb3DQEB
CwUAA4ICAQAsp5ZjkFI/jZ9GJs5915iIsTzCdociqchLVWyswXm77FNOqtQ7Shsq
rYCFF+w0PUIUiepmTvkgYu5e7NoX2kJmlWFsgrTtuXKAmt793hG3HI+GlEgNyuNo
6+l4Q9Vh+191uUskbox+1WFaKxvqBPYZZq4+knC8Hu3tIeu4riMC0m6jZJ52ZoWY
87zM/kgF7v5qhqANQL8BiRskkgxetiESuM9UTko4ZFZpXwRbBc1MYCezY0fLAZUD
6SH973ahKLAnXbz+qubWVqT2hqfeABGMa1usrAYTlrco+BBU99xinfhFpfu2hgzr
PMD421R43xDZzggC0tltXtcBkeGBCUhdjTBefvwuppPqF/nmPWqrQfXqVorwiZo0
7d4uE7SfRWXp/uwL24LQQSxxXWJnQuuDnzngPwmb5/fU2vVgvFLS+elNj2OOMJ+7
L2jC4VjlAx9e8Bg0W4Nhtr/vilmj2T+xI0VzE80wke7NDs2SsaeFmlMtMziGi2c9
+hZf9rFP7RzBaUHLtNJZPBqwriO5ydosStfJ3PmBMuIrTRiHewYe4+1dbunVQzAW
QWANeoOKeys3Gf0SsaUVpzL9xMFClwyxJ+J5owRhc/UPIBPt/sPegNFlZtLHRNvl
0/PyZkurorAY0qVmkEhCo4H1rAJvHStnrDFlsedHADlHbeqEhZA9oA==
-----END CERTIFICATE-----
)CERT";

        wifiClientSecure.setCertificate(clientCert);  // 设置客户端证书

        const char *clientKey = R"KEY(
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)KEY";
        
        wifiClientSecure.setPrivateKey(clientKey);    // 设置客户端私钥

        mqttClient.setClient(wifiClientSecure);
        DEBUG_PRINTLN("TLS enabled for MQTT with CA certificate");
    }

    // Set MQTT buffer size to handle larger messages
    mqttClient.setBufferSize(1024);     // 设置缓冲区大小
    mqttClient.setServer(this->mqttServer.c_str(), this->mqttPort);
    mqttClient.setCallback(mqttCallback);

    DEBUG_PRINTF("MQTT buffer size set to: %d bytes\n", mqttClient.getBufferSize());

    // Setup WiFi event handler
    WiFi.onEvent(bluFiEventHandler);// 注册 WiFi 事件处理程序

    initialized = true;

    DEBUG_PRINTF("ConnectivityManager initialized for device: %s\n", deviceId.c_str());
    DEBUG_PRINTF("MQTT Server: %s:%d\n", mqttServer, mqttPort);
    DEBUG_PRINTF("WiFi failure count: %d\n", wifiFailureCount);

    return true;
}

// BluFi 蓝牙配置 
bool ConnectivityManager::startBluFiProvisioning(uint32_t timeoutMs)
{
    if (!initialized)
    {
        DEBUG_PRINTLN("ConnectivityManager not initialized!");
        return false;
    }

    DEBUG_PRINTLN("Starting BluFi provisioning...");
    DEBUG_PRINTF("Service name: %s\n", BLUFI_SERVICE_NAME);
    DEBUG_PRINTF("Proof of possession: %s\n", BLUFI_POP);
    DEBUG_PRINTF("Timeout: %lu ms\n", timeoutMs);

    provisioningActive = true;

    // Generate UUID for BluFi
    uint8_t uuid[16] = {0xb4, 0xdf, 0x5a, 0x1c, 0x3f, 0x6b, 0xf4, 0xbf,
                        0xea, 0x4a, 0x82, 0x03, 0x04, 0x90, 0x1a, 0x02};

    // Start BluFi provisioning
    WiFiProv.beginProvision(
        WIFI_PROV_SCHEME_BLE,           // 使用BLE方案
        WIFI_PROV_SCHEME_HANDLER_FREE_BLE,  // 使用自由BLE处理程序
        WIFI_PROV_SECURITY_1,           // 使用安全级别1
        BLUFI_POP,               // 配对码 "abcd1234"
        BLUFI_SERVICE_NAME,      // 服务名称 "PROV_FishTank_SENSOR"
        NULL, // service_key not needed for BLE     服务密钥
        uuid,                    // 固定 UUID
        false // reset_provisioned - don't reset existing credentials   不重置现有配置
    );

    // Print QR code for easy connection    生成配对二维码
    WiFiProv.printQR(BLUFI_SERVICE_NAME, BLUFI_POP, "ble");

    // Wait for provisioning to complete or timeout
    uint32_t startTime = millis();
    while (provisioningActive && (millis() - startTime) < timeoutMs) // 等待配置完成
    {
        delay(100);
        // Check if WiFi is connected (provisioning successful)
        if (WiFi.status() == WL_CONNECTED)
        {
            DEBUG_PRINTLN("BluFi provisioning successful!");
            provisioningActive = false;
            wifiConnected = true;
            resetWiFiFailureCount(); // Reset failure count on successful connection
            return true;
        }
    }

    // Timeout reached
    if (provisioningActive)
    {
        DEBUG_PRINTLN("BluFi provisioning timeout!");
        provisioningActive = false;
        return false;
    }

    return false;
}

//  WiFi 连接管理 
bool ConnectivityManager::connectWiFi(uint32_t timeoutMs)
{
    if (wifiConnected)
    {
        return true;
    }

    // Try to connect using stored credentials
    WiFi.mode(WIFI_STA);
    WiFi.begin(); // Use stored credentials     使用存储的凭证

    DEBUG_PRINT("Connecting to WiFi using stored credentials");

    uint32_t startTime = millis();
    while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < timeoutMs) // 等待连接
    {
        delay(WIFI_RETRY_INTERVAL);
        DEBUG_PRINT(".");
    }

    if (WiFi.status() == WL_CONNECTED)
    {
        wifiConnected = true;
        onWiFiConnected(); // 连接成功处理
        resetWiFiFailureCount(); // Reset failure count on successful connection
        return true;
    }
    else
    {
        DEBUG_PRINTLN("\nWiFi connection failed!");
        incrementWiFiFailureCount(); // 更新失败计数

        // 新增：失败次数达到阈值，清除旧凭证并进入蓝牙配网
        const uint8_t WIFI_FAIL_THRESHOLD = 3;
        if (wifiFailureCount >= WIFI_FAIL_THRESHOLD)
        {
            DEBUG_PRINTLN("WiFi失败次数过多，清除旧凭证并进入BluFi配网模式...");
            clearStoredCredentials(); // 清除旧WiFi配置
            startBluFiProvisioning(60000); // 进入蓝牙配网
        }

        return false;
    }
}

// MQTT 连接管理
bool ConnectivityManager::connectMQTT(uint32_t timeoutMs)
{
    // 在连接尝试前添加服务器信息
    DEBUG_PRINTF("Connecting to MQTT server: %s:%d\n", mqttServer.c_str(), mqttPort);
    DEBUG_PRINTF("Using device ID: %s\n", jetlinksDeviceId.c_str());
    

    if (!wifiConnected)
    {
        DEBUG_PRINTLN("WiFi not connected, cannot connect to MQTT");
        return false;
    }

    if (mqttConnected && mqttClient.connected())
    {
        return true;
    }

    // Check available memory before MQTT connection
    DEBUG_PRINTF("Free heap before MQTT connection: %u bytes\n", ESP.getFreeHeap());

    // Test basic network connectivity
    DEBUG_PRINTF("WiFi status: %d, IP: %s\n", WiFi.status(), WiFi.localIP().toString().c_str());
    DEBUG_PRINTF("Connecting to MQTT broker: %s:%d\n", mqttServer.c_str(), mqttPort);

    uint32_t startTime = millis();
    int attemptCount = 0;
    const int maxAttempts = 5; // Limit number of attempts

    while (!mqttClient.connected() && (millis() - startTime) < timeoutMs && attemptCount < maxAttempts)
    {
        attemptCount++;
        DEBUG_PRINTF("MQTT connection attempt %d...\n", attemptCount);
        bool connected;

        if (mqttUser.length() > 0)
        {
            DEBUG_PRINTF("Connecting with credentials: %s\n", clientId.c_str());
            // 带认证的连接
            //connected = mqttClient.connect(clientId.c_str(), mqttUser.c_str(), mqttPassword.c_str()); // 设备 ID 作为用户名， 固定密码 "123"
            /*======================================================================================================================*/
            //connected = mqttClient.connect( jetlinksDeviceId.c_str(),nullptr,nullptr); 
            /*======================================================================================================================*/
            connected = mqttClient.connect( jetlinksDeviceId.c_str(),"1960163782781816832|1756689502661","13f7c09ab47aaa1ad8de09433298e264"); 
        }
        else
        {
            DEBUG_PRINTF("Connecting without credentials: %s\n", clientId.c_str());
            connected = mqttClient.connect(clientId.c_str());
        }

        if (connected)
        {
            mqttConnected = true;
            DEBUG_PRINTLN("MQTT connected successfully!");

            // Publish connection status
            // publishStatus(0, "connected");

            return true;
        }
        else
        {
            int state = mqttClient.state();
            DEBUG_PRINTF("MQTT connection failed, state: %d\n", state);

            // 获取TLS错误详情
            char buf[256];
            int err = wifiClientSecure.lastError(buf, sizeof(buf));
            if (err != 0) {
                DEBUG_PRINTF("TLS error: %s (code: %d)\n", buf, err);
            }else {
                DEBUG_PRINTLN("No TLS error detected.");
            }

            // 获取证书验证错误详情
            uint32_t flags = wifiClientSecure.getWriteError();
            DEBUG_PRINTF("TLS verification flags: 0x%04X\n", flags);

            // Print specific error messages
            switch (state)
            {
            case -4:
                DEBUG_PRINTLN("Connection timeout");
                break;
            case -3:
                DEBUG_PRINTLN("Connection lost");
                break;
            case -2:
                DEBUG_PRINTLN("Connect failed");
                break;
            case -1:
                DEBUG_PRINTLN("Disconnected");
                break;
            case 1:
                DEBUG_PRINTLN("Bad protocol");
                break;
            case 2:
                DEBUG_PRINTLN("Bad client ID");
                break;
            case 3:
                DEBUG_PRINTLN("Unavailable");
                break;
            case 4:
                DEBUG_PRINTLN("Bad credentials");
                break;
            case 5:
                DEBUG_PRINTLN("Unauthorized");
                break;
            default:
                DEBUG_PRINTF("Unknown error: %d\n", state);
                break;
            }
            delay(MQTT_RETRY_INTERVAL);
        }
    }

    DEBUG_PRINTF("\nMQTT connection failed after %d attempts!\n", attemptCount);
    DEBUG_PRINTF("Final MQTT state: %d\n", mqttClient.state());
    DEBUG_PRINTF("Time elapsed: %lu ms\n", millis() - startTime);
    DEBUG_PRINTF("Free heap after failed connection: %u bytes\n", ESP.getFreeHeap());

    // Clean up any partial connection state
    /*======================================================================================================================*/
    delay(500);// 新增：延迟500ms，等待Broker确认/转发
    /*======================================================================================================================*/
    mqttClient.disconnect();
    mqttConnected = false;

    return false;
}

void ConnectivityManager::disconnect()
{
    if (mqttConnected)
    {
        mqttClient.disconnect();
        mqttConnected = false;
    }

    if (wifiConnected)
    {
        WiFi.disconnect(false); // Disconnect but keep stored credentials
        wifiConnected = false;
    }

    DEBUG_PRINTLN("Disconnected from WiFi and MQTT");
}

bool ConnectivityManager::isConnected()
{
    return wifiConnected && mqttConnected && mqttClient.connected();
}

// 发布传感器数据
bool ConnectivityManager::publishSensorData(const SensorReading &reading, bool isPeriodicReport, time_t nextPeriodicReportTime)
{
    if (!isConnected())
    {
        DEBUG_PRINTLN("Not connected, cannot publish sensor data");
        return false;
    }

    String payload = createSensorDataPayload(reading, isPeriodicReport, nextPeriodicReportTime);

    //bool success = mqttClient.publish(topicSensorData.c_str(), payload.c_str(), true); // retained message

    /*======================================================================================================================*/
    bool success = mqttClient.publish(topicSensorData.c_str(), payload.c_str(), false); // 调试时用，实时显示最新消息
    /*======================================================================================================================*/

    if (success)
    {
        DEBUG_PRINTF("Published sensor data: %s\n", payload.c_str());
    }
    else
    {
        DEBUG_PRINTF("Failed to publish sensor data: %s\n", payload.c_str());
    }

    return success;
}

// 发布传感器错误
bool ConnectivityManager::publishSensorError(const char *errorMessage, time_t nextPeriodicReportTime)
{
    if (!isConnected())
    {
        DEBUG_PRINTLN("Not connected, cannot publish sensor error");
        return false;
    }

    // Create error payload similar to sensor data but with error information
    JsonDocument doc;

    // Use Beijing timestamp
    time_t beijingTime = getBeijingTimestamp();

    doc["device_id"] = deviceId;
    doc["timestamp"] = beijingTime;
    doc["beijing_time"] = getBeijingTimeString();
    doc["error"] = true;
    doc["error_message"] = errorMessage;
    doc["tds"] = nullptr;         // No valid sensor data
    doc["temperature"] = nullptr; // No valid sensor data
    doc["daily_report"] = false;
    doc["uptime_seconds"] = millis() / 1000.0;

    // Add next detection time
    if (powerManager)
    {
        time_t nextDetectionTime = beijingTime + DETECTION_INTERVAL_SECONDS;

        struct tm nextDetectionTm;
        localtime_r(&nextDetectionTime, &nextDetectionTm);
        char nextDetectionStr[64];
        strftime(nextDetectionStr, sizeof(nextDetectionStr), "%Y-%m-%d %H:%M:%S", &nextDetectionTm);

        doc["next_detection_time"] = nextDetectionStr;
        doc["next_detection_timestamp"] = nextDetectionTime;
    }

    // Add next periodic report time if provided (keep field name for compatibility)
    if (nextPeriodicReportTime > 0)
    {
        struct tm nextDailyTm;
        localtime_r(&nextPeriodicReportTime, &nextDailyTm);
        char nextDailyStr[64];
        strftime(nextDailyStr, sizeof(nextDailyStr), "%Y-%m-%d %H:%M:%S", &nextDailyTm);

        doc["next_daily_report_time"] = nextDailyStr;
        doc["next_daily_report_timestamp"] = nextPeriodicReportTime;
    }

    String payload;
    serializeJson(doc, payload);

    //bool success = mqttClient.publish(topicSensorData.c_str(), payload.c_str(), true); // retained message
    /*======================================================================================================================*/
    bool success = mqttClient.publish(topicSensorData.c_str(), payload.c_str(), false); // 调试时用，实时显示最新消息
    /*======================================================================================================================*/

    if (success)
    {
        DEBUG_PRINTF("Published sensor error: %s\n", payload.c_str());
    }
    else
    {
        DEBUG_PRINTF("Failed to publish sensor error: %s\n", payload.c_str());
    }

    return success;
}

// 发布设备状态
bool ConnectivityManager::publishStatus(uint32_t bootCount, const char *status)
{
    if (!isConnected())
    {
        return false;
    }

    String payload = createStatusPayload(bootCount, status);

    bool success = mqttClient.publish(topicStatus.c_str(), payload.c_str());

    if (success)
    {
        DEBUG_PRINTF("Published status: %s\n", payload.c_str());
    }

    return success;
}

void ConnectivityManager::loop()
{
    if (mqttConnected)
    {
        mqttClient.loop();
    }
}

String ConnectivityManager::getConnectionStatus()
{
    String status = "WiFi: ";
    status += wifiConnected ? "Connected" : "Disconnected";
    status += ", MQTT: ";
    status += mqttConnected ? "Connected" : "Disconnected";
    status += ", Failures: " + String(wifiFailureCount);

    if (wifiConnected)
    {
        status += " (IP: " + WiFi.localIP().toString() + ")";
    }

    return status;
}

bool ConnectivityManager::hasStoredCredentials()
{
    // Ensure WiFi is initialized
    if (WiFi.getMode() == WIFI_OFF)
    {
        WiFi.mode(WIFI_STA);
        delay(100); // Give WiFi time to initialize
    }

    // Check if WiFi credentials are stored in NVS
    wifi_config_t conf;
    esp_err_t err = esp_wifi_get_config(WIFI_IF_STA, &conf);

    DEBUG_PRINTF("WiFi config check: err=%d, SSID='%s'\n", err, (char *)conf.sta.ssid);

    return err == ESP_OK && (strlen((char *)conf.sta.ssid) > 0);
}

void ConnectivityManager::clearStoredCredentials()
{
    /*项目源码*/
    // DEBUG_PRINTLN("Clearing stored WiFi credentials...");
    // WiFi.disconnect(true, true); // Disconnect and erase stored credentials
    // resetWiFiFailureCount();
    /*===================清除WiFi配置===================*/
    DEBUG_PRINTLN("=== 彻底清除WiFi配置(NVS+内存)===");

    // 1. 使用ESP-IDF API彻底清除WiFi配置
    DEBUG_PRINTLN("使用esp_wifi_restore()恢复出厂设置...");
    esp_err_t err = esp_wifi_restore();
    if (err == ESP_OK) {
        DEBUG_PRINTLN(" WiFi配置已恢复出厂设置!!");
    } else {
        DEBUG_PRINTF(" 恢复出厂设置失败: %d\n", err);
    }

    // 2. 擦除NVS中所有可能的WiFi相关命名空间
    const char* wifi_namespaces[] = {"wifi", "nvs.net80211", "nvs.wifi", "esp_blufi"};
    for (const char* ns : wifi_namespaces) {
        Preferences nvsTool;
        if (nvsTool.begin(ns, false)) {
            nvsTool.clear();
            nvsTool.end();
            DEBUG_PRINTF(" 已擦除命名空间: %s\n", ns);
        }
    }

    // 3. 重启WiFi模块
    WiFi.mode(WIFI_OFF);
    delay(1000);
    WiFi.mode(WIFI_STA);
    delay(1000);

    // 4. 验证清除结果
    wifi_config_t conf;
    memset(&conf, 0, sizeof(conf));
    if (esp_wifi_get_config(WIFI_IF_STA, &conf) == ESP_OK) {
        DEBUG_PRINTF(" 清除后SSID: '%s'(长度: %d)\n", 
                     (char*)conf.sta.ssid, strlen((char*)conf.sta.ssid));
        if (strlen((char*)conf.sta.ssid) == 0) {
            DEBUG_PRINTLN(" 彻底清除成功:无残留SSID!!");
        } else {
            DEBUG_PRINTLN(" 仍有残留SSID!!");
        }
    }

    resetWiFiFailureCount();    // WiFi失败次数清零
    DEBUG_PRINTLN("=== WiFi配置清除完成 ===");

}

uint8_t ConnectivityManager::getWiFiFailureCount()
{
    return wifiFailureCount;
}

void ConnectivityManager::resetWiFiFailureCount()
{
    wifiFailureCount = 0;
    saveWiFiFailureCount();
    DEBUG_PRINTLN("WiFi failure count reset");
}

void ConnectivityManager::incrementWiFiFailureCount()
{
    wifiFailureCount++;
    saveWiFiFailureCount();
    DEBUG_PRINTF("WiFi failure count incremented to: %d\n", wifiFailureCount);
}

// 加载 WiFi 失败次数
void ConnectivityManager::loadWiFiFailureCount()
{
    // 从NVS中读取"WiFi失败次数"，若不存在则使用默认值0
    wifiFailureCount = preferences.getUChar(PREF_WIFI_FAILURE_COUNT, 0);
}

void ConnectivityManager::saveWiFiFailureCount()
{
    preferences.putUChar(PREF_WIFI_FAILURE_COUNT, wifiFailureCount);
}

void ConnectivityManager::generateDeviceId()
{
    uint8_t mac[6];
    WiFi.macAddress(mac);

    deviceId = "fish-tank-sensor-1-";
    for (int i = 0; i < 6; i++)
    {
        if (mac[i] < 16)
            deviceId += "0";
        deviceId += String(mac[i], HEX);
    }

    clientId = deviceId; // + "-" + String(millis());
}

void ConnectivityManager::setupTopics()
{
    String baseTopic = "fishtank/" + deviceId;
    //topicSensorData = baseTopic + "/sensor";

    // JetLinks 属性上报 Topic 格式: /{productId}/{deviceId}/properties/report
    topicSensorData = "/" + jetlinksProductId + "/" + jetlinksDeviceId + "/properties/report";
    //===============================================================================
    topicSensorWriteReply = "/" + jetlinksProductId + "/" + jetlinksDeviceId + "/properties/write/reply";
    //===============================================================================
    topicSensorReadReply = "/" + jetlinksProductId + "/" + jetlinksDeviceId + "/properties/read/reply";
    //===============================================================================

    topicStatus = baseTopic + "/status";
    topicHeartbeat = baseTopic + "/heartbeat";
}

void ConnectivityManager::mqttCallback(char *topic, byte *payload, unsigned int length)
{
    // For future use if we need to handle incoming MQTT messages
    DEBUG_PRINTF("MQTT message received on topic: %s\n", topic);
}

String ConnectivityManager::createSensorDataPayload(const SensorReading &reading, bool isPeriodicReport, time_t nextPeriodicReportTime)
{
    JsonDocument doc;

    // JetLinks 通常使用 timestamp 作为键，值为毫秒时间戳
    // doc["timestamp"] = getBeijingTimestamp() * 1000; // 转换为毫秒

    doc["device_id"] = deviceId;
    // 属性数据放在 properties 对象中   
    // JsonObject properties = doc.createNestedObject("properties");
    JsonObject properties = doc["properties"].to<JsonObject>();
    properties["tds"] = reading.tds;
    properties["temp"] = reading.temperature;
    properties["test"] = 10.00;           
    
    String payload;
    serializeJson(doc, payload);
    return payload;
}

String ConnectivityManager::createStatusPayload(uint32_t bootCount, const char *status)
{
    JsonDocument doc;

    doc["device_id"] = deviceId;
    doc["status"] = status;
    doc["boot_count"] = bootCount;
    doc["uptime_seconds"] = millis() / 1000.0;
    doc["free_heap"] = ESP.getFreeHeap();
    doc["wifi_rssi"] = WiFi.RSSI();

    String payload;
    serializeJson(doc, payload);
    return payload;
}

void ConnectivityManager::setPowerManager(PowerManager *pm)
{
    powerManager = pm;
}

// 时间同步功能
bool ConnectivityManager::syncTimeWithNTP()
{
    if (!wifiConnected)
    {
        DEBUG_PRINTLN("WiFi not connected, cannot sync time");
        return false;
    }

    DEBUG_PRINTLN("Syncing time with NTP servers...");

    // Configure NTP
    configTime(GMT_OFFSET_SEC, DAYLIGHT_OFFSET_SEC, NTP_SERVER1, NTP_SERVER2, NTP_SERVER3);

    // Wait for time to be set
    int retry = 0;
    const int maxRetries = 10;
    while (time(nullptr) < 8 * 3600 * 2 && retry < maxRetries)
    {
        delay(1000);
        retry++;
        DEBUG_PRINT(".");
    }

    if (retry >= maxRetries)
    {
        DEBUG_PRINTLN("\nFailed to sync time with NTP");
        return false;
    }

    DEBUG_PRINTLN("\nTime synchronized with NTP");
    DEBUG_PRINTF("Current Beijing time: %s\n", getBeijingTimeString().c_str());
    return true;
}

time_t ConnectivityManager::getBeijingTimestamp()
{
    time_t now = time(nullptr);
    if (now < 8 * 3600 * 2)
    { // If time is not set (less than 2 days since epoch)
        DEBUG_PRINTLN("Warning: Time not synchronized, using system uptime as fallback");
        return millis() / 1000; // Return seconds since boot as fallback
    }
    return now;
}

String ConnectivityManager::getBeijingTimeString()
{
    time_t now = time(nullptr);
    if (now < 8 * 3600 * 2)
    { // If time is not set
        return "Time not synchronized";
    }

    struct tm timeinfo;
    if (!localtime_r(&now, &timeinfo))
    {
        return "Time conversion failed";
    }

    char timeStr[64];
    strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", &timeinfo);
    return String(timeStr);
}

void ConnectivityManager::onWiFiConnected()
{
    DEBUG_PRINTLN();
    DEBUG_PRINTF("WiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
    DEBUG_PRINTF("Signal strength: %d dBm\n", WiFi.RSSI());

    // Sync time with NTP after WiFi connection
    syncTimeWithNTP();
}

void ConnectivityManager::onWiFiDisconnected()
{
    wifiConnected = false;
    mqttConnected = false;
    DEBUG_PRINTLN("WiFi disconnected(onWiFiDisconnected)");
}

void ConnectivityManager::bluFiEventHandler(arduino_event_t *sys_event)
{
    switch (sys_event->event_id)
    {
    case ARDUINO_EVENT_WIFI_STA_GOT_IP:
        DEBUG_PRINT("\nWiFi connected! IP address: ");
        DEBUG_PRINTLN(IPAddress(sys_event->event_info.got_ip.ip_info.ip.addr));
        if (instance)
        {
            instance->wifiConnected = true;
        }
        break;

    case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
        DEBUG_PRINTLN("\nWiFi disconnected(bluFiEventHandler)");
        if (instance)
        {
            instance->wifiConnected = false;
            instance->mqttConnected = false;
        }
        break;

    case ARDUINO_EVENT_PROV_START:
        DEBUG_PRINTLN("\nBluFi provisioning started");
        DEBUG_PRINTLN("Use the ESP BLE Provisioning app to configure WiFi");
        break;

    case ARDUINO_EVENT_PROV_CRED_RECV:
    {
        DEBUG_PRINTLN("\nReceived WiFi credentials via BluFi");
        DEBUG_PRINT("\tSSID: ");
        DEBUG_PRINTLN((const char *)sys_event->event_info.prov_cred_recv.ssid);
        DEBUG_PRINT("\tPassword: ");
        DEBUG_PRINTLN((char const *)sys_event->event_info.prov_cred_recv.password);

        wifi_config_t wifi_config;
        wifi_config.sta = sys_event->event_info.prov_cred_recv;
        esp_err_t err = esp_wifi_set_config(WIFI_IF_STA, &wifi_config);
        DEBUG_PRINTF("\nesp_wifi_set_config: %d", err);
        break;
    }
    case ARDUINO_EVENT_PROV_CRED_FAIL:
        DEBUG_PRINTLN("\nBluFi provisioning failed!");
        if (sys_event->event_info.prov_fail_reason == WIFI_PROV_STA_AUTH_ERROR)
        {
            DEBUG_PRINTLN("WiFi password incorrect");
        }
        else
        {
            DEBUG_PRINTLN("WiFi network not found");
        }
        if (instance)
        {
            instance->provisioningActive = false;
        }
        break;

    case ARDUINO_EVENT_PROV_CRED_SUCCESS:
        DEBUG_PRINTLN("\nBluFi provisioning successful!");
        break;

    case ARDUINO_EVENT_PROV_END:
        DEBUG_PRINTLN("\nBluFi provisioning ended");
        if (instance)
        {
            instance->provisioningActive = false;
        }
        break;

    default:
        break;
    }
}
