#ifndef SENSOR_DATA_MANAGER_H
#define SENSOR_DATA_MANAGER_H

#include <Arduino.h>
#include <Preferences.h>
#include "TDS_Sensor_UART.h"
#include "Config.h"

// Forward declaration
class PowerManager;

struct SensorReading {
    int tds;                    // TDS值（单位：ppm）
    float temperature;          // 温度值（单位：°C）
    unsigned long timestamp;    // 时间戳（单位：秒）
    bool valid;                 // 数据有效性标志
    
    SensorReading() : tds(0), temperature(0.0), timestamp(0), valid(false) {}
    SensorReading(int t, float temp, unsigned long ts) : tds(t), temperature(temp), timestamp(ts), valid(true) {}
};

struct ReportingState {
    time_t lastReportTime;          // 上次报告时间（Unix时间戳）
    time_t lastPeriodicReportTime;  // 上次周期性报告时间
    time_t nextPeriodicReportTime;  // 下次计划周期性报告时间
    SensorReading lastReading;      // 最后一次有效读数
    uint32_t bootCount;             // 设备启动次数

    ReportingState() : lastReportTime(0), lastPeriodicReportTime(0), nextPeriodicReportTime(0), bootCount(0) {}
};

class SensorDataManager {
public:
    SensorDataManager();
    ~SensorDataManager();

    // Initialize the data manager
    bool begin();

    // Set power manager reference for total uptime access
    void setPowerManager(PowerManager* pm);
    
    // Store a new sensor reading
    void storeReading(const SensorReading& reading);
    
    // Get the last stored reading
    SensorReading getLastReading();
    
    // Check if we should report based on thresholds
    bool shouldReportByThreshold(const SensorReading& currentReading);
    
    // Check if we should do periodic report
    bool shouldReportPeriodically();

    // Update reporting state after successful report
    void updateReportState(bool isPeriodicReport = false);

    // Get reporting state
    ReportingState getReportingState();

    // Generate next random periodic report time
    void generateNextPeriodicReportTime();

    // Get next periodic report time as Unix timestamp
    time_t getNextPeriodicReportTimestamp();

    // Get boot count
    uint32_t getBootCount();
    
    // Increment boot count
    void incrementBootCount();
    
    // Clear all stored data (for debugging)
    void clearAllData();

    // Force regenerate next periodic report time (for testing)
    void forceRegenerateNextPeriodicReport();

private:
    Preferences preferences;
    ReportingState state;
    bool initialized;
    PowerManager* powerManager;  // Reference to power manager for total uptime   指向PowerManager实例的指针，用于控制设备电源状态

    // NVS keys
    static const char* NVS_NAMESPACE;
    static const char* KEY_LAST_TDS;
    static const char* KEY_LAST_TEMP;
    static const char* KEY_LAST_TIMESTAMP;
    static const char* KEY_LAST_REPORT_TIME;
    static const char* KEY_LAST_PERIODIC_REPORT;
    static const char* KEY_NEXT_PERIODIC_REPORT;
    static const char* KEY_BOOT_COUNT;

    // Thresholds (using Config.h values)
    static const int TDS_THRESHOLD = TDS_THRESHOLD_PPM;  // ppm
    static const float TEMP_THRESHOLD;      // °C (defined in .cpp file)
    static const unsigned long PERIODIC_REPORT_INTERVAL = PERIODIC_REPORT_INTERVAL_SECONDS; // From Config.h

    // Get current timestamp (total uptime)
    uint64_t getCurrentTimestamp();

    // Load state from NVS
    void loadState();

    // Save state to NVS
    void saveState();
};

#endif // SENSOR_DATA_MANAGER_H
