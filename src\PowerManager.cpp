#include "PowerManager.h"
#include "Config.h"

// RTC memory variables (preserved during deep sleep)
RTC_DATA_ATTR uint64_t g_totalUptimeSeconds = 0;    // 总运行时间(秒)
RTC_DATA_ATTR uint64_t g_lastSleepTimeMs = 0; // Keep in ms for millis() compatibility
RTC_DATA_ATTR uint64_t g_lastSleepDurationUs = 0; // Keep in us for ESP32 API compatibility

// 无参构造 将initialized初始化为false，wakeupReason初始化为esp_sleep_get_wakeup_cause函数自动获取的值
PowerManager::PowerManager() : initialized(false) {
    wakeupReason = esp_sleep_get_wakeup_cause();
}

void PowerManager::begin() {
    initialized = true;

    // Print wake-up reason for debugging
    printWakeupReason();

    // Update total uptime if waking from deep sleep
    if (!isFirstBoot()) { // 从深度睡眠唤醒（非首次启动）
        updateTotalUptime(g_lastSleepDurationUs);// 更新总运行时间
        DEBUG_PRINTF("Updated total uptime after sleep: %llu seconds\n", g_totalUptimeSeconds);
    } else { 
        // 首次启动初始化RTC变量（非从睡眠唤醒）
        g_totalUptimeSeconds = 0;       // 总运行时间清零
        g_lastSleepTimeMs = 0;          // 上次睡眠时间清零
        g_lastSleepDurationUs = 0;      // 上次睡眠时长清零
        DEBUG_PRINTLN("First boot - initialized RTC variables");
    }

    // 配置下次深度睡眠的唤醒源：定时器唤醒
    // 睡眠时长为300-600秒之间的随机值（避免设备同步唤醒导致网络拥堵）
    esp_sleep_enable_timer_wakeup(calculateSleepDuration(300, 600));

    DEBUG_PRINTLN("PowerManager initialized");
}

// 进入深度睡眠
void PowerManager::enterDeepSleep(uint32_t sleepSeconds) {
    if (!initialized) {
        DEBUG_PRINTLN("PowerManager not initialized!");
        return;
    }

    // Calculate sleep duration     计算并保存睡眠参数
    uint64_t sleepDuration = sleepSeconds * SECOND_TO_MICROSECONDS;  //将传入的秒转换为微秒数

    // Store current time and sleep duration for next wake-up
    g_lastSleepTimeMs = millis();   //记录本次启动到当前的毫秒数
    g_lastSleepDurationUs = sleepDuration;  //即将休眠的时长(微秒)

    // Update total uptime before sleep     更新总运行时间（包含本次唤醒时段）
    g_totalUptimeSeconds = getTotalUptime();

    DEBUG_PRINTF("Current uptime: %llu seconds, entering deep sleep for %u seconds...\n",
                 g_totalUptimeSeconds, sleepSeconds);
    DEBUG_FLASH(); // Ensure all serial output is sent

    // Prepare for sleep          预处理（确保串口输出完成）
    prepareForSleep();

    // Configure timer wake-up    设置定时唤醒
    esp_sleep_enable_timer_wakeup(sleepDuration);

    // Enter deep sleep           进入睡眠
    esp_deep_sleep_start();
}

esp_sleep_wakeup_cause_t PowerManager::getWakeupReason() {
    return wakeupReason;
}

void PowerManager::printWakeupReason() {
    switch (wakeupReason) {
        case ESP_SLEEP_WAKEUP_EXT0:
            DEBUG_PRINTLN("Wakeup caused by external signal using RTC_IO");
            break;
        case ESP_SLEEP_WAKEUP_EXT1:
            DEBUG_PRINTLN("Wakeup caused by external signal using RTC_CNTL");
            break;
        case ESP_SLEEP_WAKEUP_TIMER:
            DEBUG_PRINTLN("Wakeup caused by timer");
            break;
        case ESP_SLEEP_WAKEUP_TOUCHPAD:
            DEBUG_PRINTLN("Wakeup caused by touchpad");
            break;
        case ESP_SLEEP_WAKEUP_ULP:
            DEBUG_PRINTLN("Wakeup caused by ULP program");
            break;
        default:
            DEBUG_PRINTF("Wakeup was not caused by deep sleep: %d\n", wakeupReason);
            break;
    }
}

// 随机睡眠时间生成
uint64_t PowerManager::calculateSleepDuration(uint32_t minSeconds, uint32_t maxSeconds) {
    if (maxSeconds <= minSeconds) {
        maxSeconds = minSeconds + 1;
    }

    // Generate random duration between min and max seconds
    uint32_t rangeMicroseconds = (maxSeconds - minSeconds) * 1000000;
    uint32_t randomOffset = esp_random() % rangeMicroseconds;
    uint64_t sleepDuration = (minSeconds * SECOND_TO_MICROSECONDS) + randomOffset;

    return sleepDuration;
}

// 首次启动检测
bool PowerManager::isFirstBoot() {
    DEBUG_PRINTF("Wakeup reason: %d\n", wakeupReason);
    return (wakeupReason != ESP_SLEEP_WAKEUP_TIMER && 
            wakeupReason != ESP_SLEEP_WAKEUP_EXT0 && 
            wakeupReason != ESP_SLEEP_WAKEUP_EXT1 &&
            wakeupReason != ESP_SLEEP_WAKEUP_TOUCHPAD &&
            wakeupReason != ESP_SLEEP_WAKEUP_ULP);
}

void PowerManager::prepareForSleep() {
    // Flush any remaining serial output
    DEBUG_FLASH();

    // Small delay to ensure everything is settled
    delay(100);

    // Additional cleanup can be added here if needed
    // For example: turning off peripherals, saving final state, etc.
}

// 获取总运行时间(活跃 + 睡眠)
// 深度睡眠时，millis() 不会继续计数，下次唤醒后，millis()会重新从0开始计数
uint64_t PowerManager::getTotalUptime() {
    // Current uptime is the total stored uptime plus the current session time in seconds
    return g_totalUptimeSeconds + (millis() / 1000); // 总时间(s) = RTC保存的总时间 + 本次唤醒后的运行时间
}

// 更新时间
void PowerManager::updateTotalUptime(uint64_t sleepDurationUs) {
    // Add the sleep duration (in seconds) to the total uptime
    g_totalUptimeSeconds += (sleepDurationUs / 1000000); // 将睡眠时间加入总运行时间
}
