#ifndef TDS_SENSOR_UART_H
#define TDS_SENSOR_UART_H

#include <Arduino.h>
#include <HardwareSerial.h>

class TDS_Sensor_UART {
public:
    // 用于存放读取到的 TDS 和温度值的数据结构
    struct TDS_Data {
        int tds = 0;        // TDS 值（单位：ppm）    
        float temp = 0.0f;  // 温度值（单位：°C）
        bool valid = false; // 标记数据是否有效
    };

    // 构造函数，需要传入一个 HardwareSerial 对象 (例如 Serial1, Serial2)
    TDS_Sensor_UART(HardwareSerial& serial);

    // 初始化函数，应在 setup() 中调用
    void begin(long baudrate, uint32_t config = SERIAL_8N1, int8_t rxPin = -1, int8_t txPin = -1);

    // 读取 TDS 和温度值
    // 返回一个 TDS_Data 结构体
    TDS_Data read_tds_and_temp();

    // 执行量程校准
    // 返回状态码 (0x00 表示成功)
    uint8_t calibrate();

    // 设置 NTC 热敏电阻的阻值 (单位: 欧姆)
    uint8_t set_ntc_resistance(uint32_t resistance_ohms);
    
    // 设置 NTC 热敏电阻的 B 值
    uint8_t set_ntc_b_value(uint32_t b_value);
    
    // 根据状态码获取可读的状态信息
    const char* getStatusMessage(uint8_t statusCode);

private:
    HardwareSerial* _serial;

    // 命令和响应代码
    static const uint8_t CMD_DETECT = 0xA0;
    static const uint8_t CMD_CALIBRATE = 0xA6;
    static const uint8_t CMD_SET_NTC_R = 0xA3;
    static const uint8_t CMD_SET_NTC_B = 0xA5;

    static const uint8_t RESPONSE_HEADER_MEASURE = 0xAA;
    static const uint8_t RESPONSE_HEADER_ACK = 0xAC;

    static const uint8_t STATUS_OK = 0x00;
    static const uint8_t STATUS_ERROR = 0x01;
    static const uint8_t STATUS_CAL_FAILED = 0x02;
    static const uint8_t STATUS_TEMP_OOR = 0x03; // Out Of Range

    // 私有辅助函数
    void _send_command(uint8_t command, uint32_t params = 0);
    bool _read_response(uint8_t* buffer, size_t len, uint16_t timeout_ms = 1000);
    uint8_t _execute_and_get_ack(uint8_t command, uint32_t params = 0);
    uint8_t _calculate_checksum(const uint8_t* data, size_t len);
};

#endif // TDS_SENSOR_UART_H