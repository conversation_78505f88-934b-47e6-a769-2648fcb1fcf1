; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-devkitm-1]
platform = espressif32
board = esp32-c3-devkitm-1
framework = arduino
board_build.partitions = huge_app.csv
build_flags = 
	-DARDUINO_USB_CDC_ON_BOOT=1
	-DCORE_DEBUG_LEVEL=5
	-DARDUINO_USB_MODE=1
	-Os
	-DNDEBUG
	-DDISABLE_ALL_LIBRARY_WARNINGS
	
monitor_speed = 115200
lib_deps = 
	knolleary/PubSubClient@^2.8
	bblanchon/Ard<PERSON><PERSON><PERSON><PERSON>@^7.4.2
